<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backup Files - Shans Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../Assets/performance.css">
    <script src="../Assets/performance.js"></script>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7fe;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .header-left {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .back-link {
            text-decoration: none;
            color: #007BFF;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .back-link:hover {
            color: #0056b3;
        }

        h1 {
            margin: 0;
            color: #2c3e50;
            font-size: 2.2em;
        }

        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .search-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-bar {
            padding: 10px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            width: 300px;
            transition: border-color 0.3s ease;
        }

        .search-bar:focus {
            outline: none;
            border-color: #007BFF;
        }

        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            background-color: white;
            cursor: pointer;
        }

        .stats-container {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            min-width: 150px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            font-size: 18px;
            color: #666;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007BFF;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .year-section {
            margin-bottom: 40px;
        }

        .year-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 12px 12px 0 0;
            font-size: 1.3em;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        .year-header:hover {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
        }

        .year-header.collapsed {
            border-radius: 12px;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }

        .month-container {
            background: white;
            border-radius: 0 0 12px 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .month-section {
            border-bottom: 1px solid #e1e8ed;
        }

        .month-section:last-child {
            border-bottom: none;
        }

        .month-header {
            background: #f8f9fa;
            padding: 15px 25px;
            font-weight: 600;
            color: #495057;
            border-bottom: 1px solid #e1e8ed;
        }

        .backup-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            padding: 25px;
        }

        .backup-card {
            background: white;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .backup-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #007BFF;
        }

        .backup-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007BFF, #0056b3);
        }

        .file-icon {
            font-size: 2.5em;
            color: #28a745;
            margin-bottom: 15px;
        }

        .file-name {
            font-weight: 600;
            font-size: 1.1em;
            margin-bottom: 10px;
            color: #2c3e50;
            word-break: break-word;
        }

        .file-details {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
        }

        .detail-label {
            color: #666;
            font-weight: 500;
        }

        .detail-value {
            color: #333;
            font-weight: 600;
        }

        .download-button {
            width: 100%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .download-button:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
            transform: translateY(-2px);
        }

        .download-button:active {
            transform: translateY(0);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 4em;
            color: #ddd;
            margin-bottom: 20px;
        }

        .empty-message {
            font-size: 1.2em;
            margin-bottom: 10px;
        }

        .empty-submessage {
            font-size: 0.9em;
            color: #999;
        }

        /* Toast notification styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .toast {
            background-color: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 400px;
            word-wrap: break-word;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast.success {
            background-color: #28a745;
        }

        .toast.error {
            background-color: #dc3545;
        }

        .toast.info {
            background-color: #17a2b8;
        }

        .logout-button {
            background-color: #e74c3c;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        .logout-button:hover {
            background-color: #c0392b;
        }

        .refresh-button {
            background-color: #007BFF;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.3s ease;
        }

        .refresh-button:hover {
            background-color: #0056b3;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
                margin-bottom: 20px;
            }

            .header-left {
                width: 100%;
            }

            .controls {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
                margin-bottom: 20px;
            }

            .search-container {
                flex-direction: column;
                gap: 8px;
            }

            .search-bar {
                width: 100%;
                font-size: 16px; /* Prevents zoom on iOS */
                padding: 12px 15px;
            }

            .filter-select {
                width: 100%;
                font-size: 16px;
                padding: 12px 15px;
            }

            .stats-container {
                justify-content: center;
                gap: 10px;
                margin-bottom: 20px;
            }

            .stat-card {
                min-width: 120px;
                padding: 15px;
                flex: 1;
            }

            .stat-number {
                font-size: 1.5em;
            }

            .stat-label {
                font-size: 0.8em;
            }

            .backup-grid {
                grid-template-columns: 1fr;
                padding: 15px;
                gap: 15px;
            }

            .backup-card {
                padding: 15px;
            }

            .file-icon {
                font-size: 2em;
                margin-bottom: 10px;
            }

            .file-name {
                font-size: 1em;
                margin-bottom: 8px;
            }

            .file-details {
                gap: 6px;
                margin-bottom: 15px;
            }

            .detail-item {
                font-size: 0.85em;
            }

            .download-button {
                padding: 10px 15px;
                font-size: 13px;
            }

            h1 {
                font-size: 1.6em;
                margin-bottom: 5px;
            }

            .year-header {
                padding: 12px 20px;
                font-size: 1.1em;
            }

            .month-header {
                padding: 12px 20px;
                font-size: 0.95em;
            }

            .back-link {
                font-size: 14px;
            }

            .empty-state {
                padding: 40px 15px;
            }

            .empty-icon {
                font-size: 3em;
            }

            .empty-message {
                font-size: 1.1em;
            }

            .empty-submessage {
                font-size: 0.85em;
            }

            .logout-button {
                padding: 6px 12px;
                font-size: 12px;
            }

            .refresh-button {
                padding: 8px 16px;
                font-size: 13px;
                width: 100%;
                justify-content: center;
            }
        }

        /* Extra small devices */
        @media (max-width: 480px) {
            .container {
                padding: 8px;
            }

            h1 {
                font-size: 1.4em;
            }

            .stats-container {
                flex-direction: column;
                align-items: stretch;
            }

            .stat-card {
                min-width: auto;
                text-align: center;
            }

            .backup-grid {
                padding: 10px;
                gap: 12px;
            }

            .backup-card {
                padding: 12px;
            }

            .file-name {
                font-size: 0.9em;
                line-height: 1.3;
            }

            .detail-item {
                font-size: 0.8em;
                flex-direction: column;
                align-items: flex-start;
                gap: 2px;
            }

            .detail-label {
                font-size: 0.75em;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .detail-value {
                font-size: 0.85em;
            }

            .year-header {
                padding: 10px 15px;
                font-size: 1em;
            }

            .month-header {
                padding: 10px 15px;
                font-size: 0.9em;
            }

            .search-bar, .filter-select {
                padding: 10px 12px;
            }

            .toast {
                max-width: calc(100vw - 20px);
                margin: 0 10px;
            }

            .logout-button {
                padding: 5px 10px;
                font-size: 11px;
            }

            .refresh-button {
                padding: 6px 12px;
                font-size: 12px;
            }

            .header {
                margin-bottom: 15px;
            }

            .controls {
                margin-bottom: 15px;
            }
        }

        /* Landscape orientation on small devices */
        @media (max-width: 768px) and (orientation: landscape) {
            .stats-container {
                flex-direction: row;
                justify-content: space-between;
            }

            .stat-card {
                flex: 1;
                margin: 0 5px;
            }

            .backup-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner-large"></div>
            <div class="loading-text">Loading Backup Files...</div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container"></div>

    <div class="container">
        <div class="header">
            <div class="header-left">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Dashboard
                </a>
                <h1>Backup Files</h1>
            </div>
            <button onclick="logout()" class="logout-button">
                Logout
            </button>
        </div>

        <div class="controls">
            <div class="search-container">
                <input type="text" class="search-bar" id="searchInput" placeholder="Search backup files...">
                <select class="filter-select" id="yearFilter">
                    <option value="">All Years</option>
                </select>
            </div>
            <button onclick="refreshBackups()" class="refresh-button">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>

        <div class="stats-container" id="statsContainer">
            <div class="stat-card">
                <div class="stat-number" id="totalFiles">0</div>
                <div class="stat-label">Total Files</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalSize">0 MB</div>
                <div class="stat-label">Total Size</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="latestBackup">-</div>
                <div class="stat-label">Latest Backup</div>
            </div>
        </div>

        <div id="loadingContainer" class="loading-container" style="display: none;">
            <div class="loading-spinner"></div>
            Loading backup files...
        </div>

        <div id="backupContainer">
            <!-- Backup files will be populated here -->
        </div>

        <div id="emptyState" class="empty-state" style="display: none;">
            <div class="empty-icon">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="empty-message">No backup files found</div>
            <div class="empty-submessage">Backup files will appear here once you create them from the Sales Dashboard</div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000api';
        let allBackups = [];
        let filteredBackups = [];

        // Check authentication status
        async function checkAuthStatus() {
            const token = localStorage.getItem('authToken');
            const userInfo = localStorage.getItem('userInfo');

            if (!token || !userInfo) {
                window.location.href = '../login.html';
                return false;
            }

            const user = JSON.parse(userInfo);
            if (!user.is_admin) {
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
                window.location.href = '../login.html';
                return false;
            }

            return true;
        }

        // Logout function
        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('userInfo');
            window.location.href = '../login.html';
        }

        // Show toast notification
        function showToast(message, type = 'info', duration = 5000) {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;

            toastContainer.appendChild(toast);

            // Trigger animation
            setTimeout(() => toast.classList.add('show'), 100);

            // Remove toast after duration
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, duration);
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Extract month and year from filename or context
        function extractMonthYear(backup) {
            if (backup.month && backup.month !== 'Unknown') {
                return backup.month;
            }

            // Try to extract from filename
            const filename = backup.filename;
            const match = filename.match(/(\w+_\d{4})/);
            if (match) {
                return match[1].replace('_', ' ');
            }

            // Fallback to upload date
            const date = new Date(backup.uploadedAt);
            return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
        }

        // Load backup files from API
        async function loadBackupFiles() {
            const loadingContainer = document.getElementById('loadingContainer');
            const backupContainer = document.getElementById('backupContainer');
            const emptyState = document.getElementById('emptyState');

            try {
                loadingContainer.style.display = 'flex';
                backupContainer.innerHTML = '';
                emptyState.style.display = 'none';

                const token = localStorage.getItem('authToken');
                const userInfo = localStorage.getItem('userInfo');

                console.log('Debug info:');
                console.log('- Token exists:', !!token);
                console.log('- Token length:', token ? token.length : 0);
                console.log('- User info exists:', !!userInfo);
                if (userInfo) {
                    try {
                        const user = JSON.parse(userInfo);
                        console.log('- User email:', user.email);
                        console.log('- Is admin:', user.is_admin);
                    } catch (e) {
                        console.log('- User info parse error:', e.message);
                    }
                }

                if (!token) {
                    throw new Error('Authentication required - no token found in localStorage');
                }

                console.log('Fetching backup files from:', `${API_BASE_URL}/backup/list-backups`);

                const response = await fetch(`${API_BASE_URL}/backup/list-backups`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                if (!response.ok) {
                    // Try to get the response body for more details
                    let errorDetails = '';
                    try {
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            const errorData = await response.json();
                            errorDetails = errorData.message || errorData.error || JSON.stringify(errorData);
                        } else {
                            errorDetails = await response.text();
                        }
                    } catch (e) {
                        errorDetails = 'Could not read error details';
                    }

                    console.log('Error response details:', errorDetails);

                    // Handle different error types
                    if (response.status === 403) {
                        throw new Error(`Access forbidden (403). Details: ${errorDetails}. Please try clicking "Test Auth" button or log out and log back in.`);
                    } else if (response.status === 404) {
                        throw new Error(`Backup API endpoint not found (404). Details: ${errorDetails}. Please ensure the backup routes are deployed to your backend.`);
                    } else if (response.status === 401) {
                        throw new Error(`Authentication required (401). Details: ${errorDetails}. Please log out and log back in.`);
                    } else {
                        throw new Error(`Server error (${response.status}): ${response.statusText}. Details: ${errorDetails}`);
                    }
                }

                const result = await response.json();
                console.log('Response data:', result);

                allBackups = result.backups || [];

                if (allBackups.length === 0) {
                    emptyState.style.display = 'block';
                } else {
                    updateStats();
                    populateYearFilter();
                    displayBackups(allBackups);
                }

            } catch (error) {
                console.error('Error loading backup files:', error);
                showToast(`Failed to load backup files: ${error.message}`, 'error');
                emptyState.style.display = 'block';
            } finally {
                loadingContainer.style.display = 'none';
            }
        }

        // Update statistics
        function updateStats() {
            const totalFiles = allBackups.length;
            const totalSize = allBackups.reduce((sum, backup) => sum + (backup.size || 0), 0);
            const latestBackup = allBackups.length > 0 ?
                new Date(Math.max(...allBackups.map(b => new Date(b.uploadedAt)))) : null;

            document.getElementById('totalFiles').textContent = totalFiles;
            document.getElementById('totalSize').textContent = formatFileSize(totalSize);
            document.getElementById('latestBackup').textContent = latestBackup ?
                formatDate(latestBackup) : '-';
        }

        // Populate year filter dropdown
        function populateYearFilter() {
            const yearFilter = document.getElementById('yearFilter');
            const years = [...new Set(allBackups.map(backup => {
                const date = new Date(backup.uploadedAt);
                return date.getFullYear();
            }))].sort((a, b) => b - a);

            // Clear existing options except "All Years"
            yearFilter.innerHTML = '<option value="">All Years</option>';

            years.forEach(year => {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearFilter.appendChild(option);
            });
        }

        // Display backup files organized by year and month
        function displayBackups(backups) {
            const backupContainer = document.getElementById('backupContainer');
            backupContainer.innerHTML = '';

            if (backups.length === 0) {
                document.getElementById('emptyState').style.display = 'block';
                return;
            }

            document.getElementById('emptyState').style.display = 'none';

            // Group backups by year and month
            const groupedBackups = {};

            backups.forEach(backup => {
                const date = new Date(backup.uploadedAt);
                const year = date.getFullYear();
                const monthYear = extractMonthYear(backup);

                if (!groupedBackups[year]) {
                    groupedBackups[year] = {};
                }

                if (!groupedBackups[year][monthYear]) {
                    groupedBackups[year][monthYear] = [];
                }

                groupedBackups[year][monthYear].push(backup);
            });

            // Sort years in descending order
            const sortedYears = Object.keys(groupedBackups).sort((a, b) => b - a);

            sortedYears.forEach(year => {
                const yearSection = document.createElement('div');
                yearSection.className = 'year-section';

                const yearHeader = document.createElement('div');
                yearHeader.className = 'year-header';
                yearHeader.innerHTML = `
                    <span>${year}</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                `;

                const monthContainer = document.createElement('div');
                monthContainer.className = 'month-container';

                // Sort months within the year (newest month first: June before May, etc.)
                const sortedMonths = Object.keys(groupedBackups[year]).sort((a, b) => {
                    // Parse month names to get month indices (January=0, February=1, ..., December=11)
                    const getMonthIndex = (monthYearStr) => {
                        // Extract month name from strings like "June 2024" or "June_2024"
                        const monthName = monthYearStr.split(/[\s_]/)[0];
                        const monthIndex = new Date(Date.parse(monthName + " 1, 2000")).getMonth();
                        return monthIndex;
                    };

                    const monthIndexA = getMonthIndex(a);
                    const monthIndexB = getMonthIndex(b);

                    // Sort by month index descending (June=5, May=4, so June comes first)
                    return monthIndexB - monthIndexA;
                });

                sortedMonths.forEach(monthYear => {
                    const monthSection = document.createElement('div');
                    monthSection.className = 'month-section';

                    const monthHeader = document.createElement('div');
                    monthHeader.className = 'month-header';
                    monthHeader.textContent = monthYear;

                    const backupGrid = document.createElement('div');
                    backupGrid.className = 'backup-grid';

                    // Display backup files within the month (no additional sorting needed)
                    groupedBackups[year][monthYear].forEach(backup => {
                        const backupCard = createBackupCard(backup);
                        backupGrid.appendChild(backupCard);
                    });

                    monthSection.appendChild(monthHeader);
                    monthSection.appendChild(backupGrid);
                    monthContainer.appendChild(monthSection);
                });

                // Add click handler for year header
                yearHeader.addEventListener('click', () => {
                    const isCollapsed = monthContainer.style.display === 'none';
                    monthContainer.style.display = isCollapsed ? 'block' : 'none';
                    yearHeader.classList.toggle('collapsed', !isCollapsed);
                    yearHeader.querySelector('.toggle-icon').classList.toggle('collapsed', !isCollapsed);
                });

                yearSection.appendChild(yearHeader);
                yearSection.appendChild(monthContainer);
                backupContainer.appendChild(yearSection);
            });
        }

        // Create backup card element
        function createBackupCard(backup) {
            const card = document.createElement('div');
            card.className = 'backup-card';

            const uploadDate = formatDate(backup.uploadedAt);
            const fileSize = formatFileSize(backup.size || 0);
            const recordCount = backup.recordCount || 'Unknown';
            const backedUpBy = backup.backedUpBy || 'Unknown';

            card.innerHTML = `
                <div class="file-icon">
                    <i class="fas fa-file-excel"></i>
                </div>
                <div class="file-name">${backup.filename}</div>
                <div class="file-details">
                    <div class="detail-item">
                        <span class="detail-label">Upload Date:</span>
                        <span class="detail-value">${uploadDate}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">File Size:</span>
                        <span class="detail-value">${fileSize}</span>
                    </div>
                </div>
                <button class="download-button" onclick="downloadBackup('${backup.url}', '${backup.filename}')">
                    <i class="fas fa-download"></i>
                    Download
                </button>
            `;

            return card;
        }

        // Download backup file
        function downloadBackup(url, filename) {
            try {
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showToast(`Downloading ${filename}...`, 'success', 3000);
            } catch (error) {
                console.error('Download error:', error);
                showToast(`Failed to download ${filename}`, 'error');
            }
        }

        // Search and filter functionality
        function filterBackups() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const selectedYear = document.getElementById('yearFilter').value;

            filteredBackups = allBackups.filter(backup => {
                const matchesSearch = !searchTerm ||
                    backup.filename.toLowerCase().includes(searchTerm) ||
                    extractMonthYear(backup).toLowerCase().includes(searchTerm) ||
                    (backup.backedUpBy && backup.backedUpBy.toLowerCase().includes(searchTerm));

                const matchesYear = !selectedYear ||
                    new Date(backup.uploadedAt).getFullYear().toString() === selectedYear;

                return matchesSearch && matchesYear;
            });

            displayBackups(filteredBackups);
        }



        // Refresh backups
        async function refreshBackups() {
            showToast('Refreshing backup files...', 'info', 2000);
            await loadBackupFiles();
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', async function() {
            // Hide loading screen
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                loadingScreen.style.display = 'none';
            }

            // Check authentication
            const isAuthenticated = await checkAuthStatus();
            if (!isAuthenticated) return;

            // Load backup files
            await loadBackupFiles();

            // Add search functionality
            const searchInput = document.getElementById('searchInput');
            const yearFilter = document.getElementById('yearFilter');

            searchInput.addEventListener('input', filterBackups);
            yearFilter.addEventListener('change', filterBackups);
        });
    </script>
</body>
</html>
