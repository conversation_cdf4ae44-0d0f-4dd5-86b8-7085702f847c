<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Shans Inventory System - Sales History</title>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #f5f5f5;
            --text-color: #333;
            --border-color: #ddd;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--secondary-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .controls {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .controls-left, .controls-right {
            display: flex;
            gap: 10px;
        }

        .view-toggle, .search-bar, .action-button {
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 5px;
        }

        .view-toggle, .action-button {
            background-color: var(--primary-color);
            color: white;
            cursor: pointer;
            transition: background-color 0.3s ease;
            white-space: nowrap;
        }

        .view-toggle:hover, .action-button:hover {
            background-color: #357ab8;
        }

        .print-button {
            background-color: #28a745;
        }

        .print-button:hover {
            background-color: #218838;
        }

        .excel-button {
            background-color: #217346;
        }

        .excel-button:hover {
            background-color: #1e6e41;
        }

        .backup-button {
            background-color: #6f42c1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
            transition: background-color 0.3s ease;
        }

        .backup-button:hover {
            background-color: #5a2d91;
        }

        .backup-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        /* Toast notification styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .toast {
            background-color: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 400px;
            word-wrap: break-word;
        }

        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast.success {
            background-color: #28a745;
        }

        .toast.error {
            background-color: #dc3545;
        }

        .toast.info {
            background-color: #17a2b8;
        }

        .search-bar {
            width: 300px;
            transition: all 0.3s ease;
            flex-grow: 1;
        }

        .search-bar:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }

        /* Filter Controls Styling */
        .filter-controls {
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }

        .filter-group label {
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-color);
            font-size: 14px;
        }

        .filter-select {
            padding: 8px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: white;
            font-size: 14px;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
        }

        .clear-filters-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
            height: fit-content;
        }

        .clear-filters-btn:hover {
            background-color: #5a6268;
        }

        .month-section {
            margin-bottom: 40px;
        }

        .month-header {
            background-color: #f8f9fa;
            color: #333;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 600;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        /* Excel-like table styling */
        .table-container {
            overflow-x: auto;
            max-width: 100%;
            margin-bottom: 20px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            font-size: 14px;
            border: 1px solid #e0e0e0;
            margin-bottom: 0;
        }

        th, td {
            text-align: left;
            padding: 8px 10px;
            border: 1px solid #e0e0e0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        th {
            position: sticky;
            top: 0;
            background-color: #f2f2f2;
            color: #333;
            font-weight: bold;
            border-bottom: 2px solid #d0d0d0;
            z-index: 10;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #f0f7ff;
        }

        /* Column specific styling */
        td:nth-child(1), th:nth-child(1) { /* Reference Number */
            min-width: 120px;
            font-weight: 500;
        }

        td:nth-child(2), th:nth-child(2) { /* Date */
            min-width: 100px;
        }

        td:nth-child(3), th:nth-child(3) { /* Client Name */
            min-width: 150px;
            font-weight: 500;
        }

        td:nth-child(4), th:nth-child(4) { /* Billing Address */
            min-width: 200px;
        }

        td:nth-child(5), th:nth-child(5) { /* Billing Email */
            min-width: 180px;
        }

        td:nth-child(7), th:nth-child(7) { /* Salesperson */
            min-width: 120px;
        }

        td:nth-child(8), th:nth-child(8) { /* Company */
            min-width: 150px;
        }

        td:nth-child(9), th:nth-child(9) { /* Item Purchase */
            min-width: 180px;
        }

        td:nth-child(11), th:nth-child(11), /* Quantity */
        td:nth-child(12), th:nth-child(12), /* Unit Cost */
        td:nth-child(13), th:nth-child(13), /* Unit Price */
        td:nth-child(14), th:nth-child(14), /* Profit per Unit */
        td:nth-child(15), th:nth-child(15), /* Tax per Unit */
        td:nth-child(17), th:nth-child(17), /* Total */
        td:nth-child(18), th:nth-child(18) { /* Total Profit */
            text-align: right;
            min-width: 100px;
        }

        /* Profit columns styling */
        td:nth-child(14), td:nth-child(18) {
            background-color: #f0fff0;
        }

        /* Month Summary Section */
        .month-summary {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            margin-top: 15px;
            margin-bottom: 30px;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        /* Mobile-specific summary that appears at the top */
        .month-summary-mobile {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            margin-top: 10px;
            margin-bottom: 15px;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            display: none; /* Hidden by default */
            flex-direction: column;
            gap: 10px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .summary-label {
            font-weight: 600;
            color: #555;
        }

        .summary-value {
            font-weight: bold;
            color: #333;
        }

        .grand-total {
            background-color: #e6f2ff;
            border-left: 4px solid #0066cc;
        }

        .grand-total .summary-label {
            font-size: 16px;
            color: #333;
        }

        .grand-total .summary-value {
            font-size: 18px;
            color: #0066cc;
        }

        .profit-total {
            background-color: #e6ffe6;
            border-left: 4px solid #28a745;
        }

        .profit-total .summary-label {
            font-size: 15px;
            color: #333;
        }

        .profit-total .summary-value {
            font-size: 16px;
            color: #28a745;
        }

        .card-view {
            display: none;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            font-size: 18px;
        }

        .card p {
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
        }

        .card p strong {
            font-weight: 600;
            color: #555;
            margin-right: 10px;
        }

        @media (max-width: 768px) {
            .controls-left, .controls-right {
                width: 100%;
            }

            .controls-left {
                display: grid;
                grid-template-columns: 1fr 1fr;
            }

            .search-bar {
                width: 100%;
            }

            /* Mobile filter controls */
            .filter-row {
                flex-direction: column;
                gap: 10px;
            }

            .filter-group {
                min-width: 100%;
            }

            .filter-select {
                width: 100%;
            }

            .table-container {
                overflow-x: auto;
                max-width: 100%;
            }

            /* Auto-switch to card view on mobile */
            #tables-container {
                display: none !important;
            }

            .card-view {
                display: grid !important;
                grid-template-columns: 1fr;
            }

            .card {
                margin-bottom: 15px;
            }

            /* Hide the regular summary at the bottom on mobile */
            .month-summary {
                display: none !important;
            }

            /* Show the mobile summary at the top on mobile */
            .month-summary-mobile {
                display: flex !important;
                margin-top: 10px;
                margin-bottom: 15px;
            }

            .summary-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .summary-value {
                align-self: flex-end;
                width: 100%;
                text-align: right;
            }
        }

        /* Print button styling */
        @media print {
            body * {
                visibility: visible;
            }
        }
    </style>
</head>
<body>
    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <div class="container">
        <a href="index.html" style="text-decoration: none; color: #007BFF; font-size: 16px; margin-right: 20px;">&larr; Back to Home</a>
        <h1>Sales Dashboard</h1>
        <div class="controls">
            <div class="controls-left">
                <button class="view-toggle">Toggle View</button>
                <button class="action-button print-button" id="print-button">Print</button>
                <button class="action-button excel-button" id="save-to-excel">Export to Excel</button>
            </div>
            <div class="controls-right">
                <input type="text" class="search-bar" placeholder="Search sales by any field...">
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="filter-controls">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="company-filter">Company Name:</label>
                    <select id="company-filter" class="filter-select">
                        <option value="">All Companies</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="payment-filter">Payment Method:</label>
                    <select id="payment-filter" class="filter-select">
                        <option value="">All Methods</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="salesperson-filter">Salesperson:</label>
                    <select id="salesperson-filter" class="filter-select">
                        <option value="">All Salespersons</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="month-filter">Sales Month:</label>
                    <select id="month-filter" class="filter-select">
                        <option value="">All Months</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button id="clear-filters" class="clear-filters-btn">Clear Filters</button>
                </div>
            </div>
        </div>
        <div id="tables-container">
            <!-- Monthly Tables Will Be Dynamically Added Here -->
        </div>
        <div class="card-view" id="card-view">
            <!-- Cards Will Be Dynamically Added Here -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/xlsx/dist/xlsx.full.min.js"></script>
    <style media="print">
        /* Print-specific styles */
        body {
            font-size: 12px;
            background-color: white;
        }

        .container {
            max-width: 100%;
            padding: 0;
            margin: 0;
        }

        .controls, .view-toggle, #print-button, #save-to-excel, a[href="index.html"] {
            display: none !important;
        }

        .month-header {
            background-color: #f2f2f2 !important;
            color: black !important;
            padding: 10px !important;
            margin-bottom: 10px !important;
            page-break-before: always;
            border-left: 3px solid #333 !important;
            font-size: 16px !important;
            font-weight: bold !important;
        }

        table {
            width: 100% !important;
            border-collapse: collapse !important;
            page-break-inside: avoid;
            font-size: 10px !important;
        }

        th {
            background-color: #f2f2f2 !important;
            color: black !important;
            font-weight: bold !important;
        }

        th, td {
            border: 1px solid #ddd !important;
            padding: 4px !important;
        }

        .month-summary {
            margin-top: 10px !important;
            margin-bottom: 20px !important;
            padding: 10px !important;
            border: 1px solid #ddd !important;
            page-break-inside: avoid;
        }

        .summary-item {
            padding: 5px 10px !important;
            margin-bottom: 5px !important;
            border: 1px solid #eee !important;
        }

        .grand-total {
            background-color: #f2f2f2 !important;
            border-left: 3px solid #333 !important;
        }

        .profit-total {
            background-color: #f2f2f2 !important;
            border-left: 3px solid #28a745 !important;
        }

        .card-view {
            display: none !important;
        }
    </style>

    <script>
        document.getElementById('save-to-excel').addEventListener('click', saveToExcel);
        document.getElementById('print-button').addEventListener('click', printSalesData);

        const tablesContainer = document.getElementById('tables-container');
        const cardView = document.getElementById('card-view');
        const viewToggle = document.querySelector('.view-toggle');
        const searchBar = document.querySelector('.search-bar');

        // Filter elements
        const companyFilter = document.getElementById('company-filter');
        const paymentFilter = document.getElementById('payment-filter');
        const salespersonFilter = document.getElementById('salesperson-filter');
        const monthFilter = document.getElementById('month-filter');
        const clearFiltersBtn = document.getElementById('clear-filters');

        let allSalesData = [];
        let groupedSalesData = {};
        let filteredSalesData = [];
        let monthlyExpenses = {}; // Store expense totals by month

        // Auto-detect environment and set appropriate API URL
        function getApiBaseUrl() {
          const hostname = window.location.hostname;

          // Production environment (Netlify)
          if (hostname === 'shans-system.netlify.app') {
            return 'http://localhost:8000api';
          }

          // Local development
          if (hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'http://localhost:8000api';
          }

          // Default fallback to production
          return 'http://localhost:8000api';
        }

        async function fetchSalesData() {
            try {
                const response = await fetch(`${getApiBaseUrl()}/sales`);
                if (!response.ok) {
                    throw new Error('Network response was not ok.');
                }
                const data = await response.json();
                console.log('Raw data from API:', data);

                // Fetch all products to get room information
                const productsResponse = await fetch(`${getApiBaseUrl()}/products`);
                if (!productsResponse.ok) {
                    throw new Error('Failed to fetch products data.');
                }
                const productsData = await productsResponse.json();

                // Create maps for product information lookup
                const productRoomMap = {};
                const productCostMap = {};
                productsData.forEach(product => {
                    productRoomMap[product.item_code] = product.room_name;
                    productCostMap[product.item_code] = {
                        unit_cost: parseFloat(product.unit_cost) || 0,
                        unit_retail_price: parseFloat(product.unit_retail_price) || 0
                    };
                });

                // Add room information to each sale item
                data.forEach(sale => {
                    sale.items.forEach(item => {
                        // Add room information
                        item.room_name = productRoomMap[item.item_code] || 'N/A';

                        // Get cost information from product data if available
                        if (productCostMap[item.item_code]) {
                            // Store the unit cost from the product data for profit calculations
                            item.unit_cost = productCostMap[item.item_code].unit_cost;
                        } else {
                            // If product not found, ensure unit_cost is set to 0 to avoid NaN in calculations
                            item.unit_cost = 0;
                        }
                    });
                });

                allSalesData = data;
                filteredSalesData = [...allSalesData];
                populateFilterDropdowns();
                await fetchMonthlyExpenses();
                groupDataByMonth(filteredSalesData);
                renderTables(groupedSalesData);
                renderCards(filteredSalesData);
            } catch (error) {
                console.error('Error fetching sales data:', error);
                tablesContainer.innerHTML = '<p style="color: red;">Failed to load sales data. Please try again later.</p>';
            }
        }

        function groupDataByMonth(data) {
            groupedSalesData = {};
            data.forEach(item => {
                const date = new Date(item.date);
                const monthYear = date.toLocaleString('default', { month: 'long', year: 'numeric' });
                if (!groupedSalesData[monthYear]) {
                    groupedSalesData[monthYear] = [];
                }
                groupedSalesData[monthYear].push(item);
            });
        }

        function populateFilterDropdowns() {
            // Get unique values for each filter
            const companies = [...new Set(allSalesData.map(sale => sale.company_name || 'Shans Accessories PTY LTD'))].sort();
            const paymentMethods = [...new Set(allSalesData.map(sale => sale.payment_method))].filter(method => method).sort();
            const salespersons = [...new Set(allSalesData.map(sale => sale.salesperson_name))].filter(person => person).sort();
            const months = [...new Set(allSalesData.map(sale => {
                const date = new Date(sale.date);
                return date.toLocaleString('default', { month: 'long', year: 'numeric' });
            }))].sort((a, b) => new Date(a) - new Date(b));

            // Populate company filter
            companyFilter.innerHTML = '<option value="">All Companies</option>';
            companies.forEach(company => {
                const option = document.createElement('option');
                option.value = company;
                option.textContent = company;
                companyFilter.appendChild(option);
            });

            // Populate payment method filter
            paymentFilter.innerHTML = '<option value="">All Methods</option>';
            paymentMethods.forEach(method => {
                const option = document.createElement('option');
                option.value = method;
                option.textContent = method;
                paymentFilter.appendChild(option);
            });

            // Populate salesperson filter
            salespersonFilter.innerHTML = '<option value="">All Salespersons</option>';
            salespersons.forEach(person => {
                const option = document.createElement('option');
                option.value = person;
                option.textContent = person;
                salespersonFilter.appendChild(option);
            });

            // Populate month filter
            monthFilter.innerHTML = '<option value="">All Months</option>';
            months.forEach(month => {
                const option = document.createElement('option');
                option.value = month;
                option.textContent = month;
                monthFilter.appendChild(option);
            });
        }

        async function fetchMonthlyExpenses() {
            try {
                console.log('Fetching month-specific expenses...');

                // Get unique months from sales data
                const uniqueMonths = [...new Set(allSalesData.map(sale => {
                    const date = new Date(sale.date);
                    return date.toLocaleString('default', { month: 'long', year: 'numeric' });
                }))];

                monthlyExpenses = {};

                // For each month, fetch expenses specific to that month
                // This will include recurring expenses (appear in all months) + non-recurring expenses (only in their specific month)
                for (const monthYear of uniqueMonths) {
                    try {
                        // Parse month and year from monthYear string (e.g., "January 2024")
                        const date = new Date(monthYear + ' 1'); // Add day to make it a valid date
                        const year = date.getFullYear();
                        const month = date.getMonth() + 1; // getMonth() returns 0-11, we need 1-12

                        console.log(`Fetching expenses for ${monthYear} (year: ${year}, month: ${month})`);

                        // Validate the date parsing
                        if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
                            console.error(`Invalid date parsing for ${monthYear}: year=${year}, month=${month}`);
                            monthlyExpenses[monthYear] = 0;
                            continue;
                        }

                        // Fetch expenses for this specific month using the existing API endpoint
                        const response = await fetch(`${getApiBaseUrl()}/expenses?year=${year}&month=${month}`);

                        if (!response.ok) {
                            throw new Error(`Failed to fetch expenses for ${monthYear}: ${response.status} ${response.statusText}`);
                        }

                        const monthExpenses = await response.json();
                        console.log(`Expenses for ${monthYear}:`, monthExpenses);

                        // Separate recurring and non-recurring expenses for debugging
                        const recurringExpenses = monthExpenses.filter(expense =>
                            expense.is_recurring || expense.recurring_template_id
                        );
                        const nonRecurringExpenses = monthExpenses.filter(expense =>
                            !expense.is_recurring && !expense.recurring_template_id
                        );

                        const recurringTotal = recurringExpenses.reduce((total, expense) => {
                            return total + parseFloat(expense.amount || 0);
                        }, 0);

                        const nonRecurringTotal = nonRecurringExpenses.reduce((total, expense) => {
                            return total + parseFloat(expense.amount || 0);
                        }, 0);

                        // Calculate total for this month
                        const monthTotal = monthExpenses.reduce((total, expense) => {
                            const amount = parseFloat(expense.amount || 0);
                            return total + amount;
                        }, 0);

                        monthlyExpenses[monthYear] = monthTotal;

                        console.log(`=== EXPENSE BREAKDOWN FOR ${monthYear} ===`);
                        console.log(`Recurring expenses: R${recurringTotal} (${recurringExpenses.length} items)`);
                        console.log(`Non-recurring expenses: R${nonRecurringTotal} (${nonRecurringExpenses.length} items)`);
                        console.log(`Total expenses: R${monthTotal} (${monthExpenses.length} total items)`);
                        console.log(`=======================================`);

                    } catch (monthError) {
                        console.error(`Error fetching expenses for ${monthYear}:`, monthError);
                        monthlyExpenses[monthYear] = 0;
                    }
                }

                console.log('Final monthly expenses object:', monthlyExpenses);

            } catch (error) {
                console.error('Error in fetchMonthlyExpenses:', error);

                // Fallback: set all monthly expenses to 0
                const uniqueMonths = [...new Set(allSalesData.map(sale => {
                    const date = new Date(sale.date);
                    return date.toLocaleString('default', { month: 'long', year: 'numeric' });
                }))];

                monthlyExpenses = {};
                for (const monthYear of uniqueMonths) {
                    monthlyExpenses[monthYear] = 0;
                }
            }
        }

        function renderTables(groupedData) {
            tablesContainer.innerHTML = '';

            if (Object.keys(groupedData).length === 0) {
                tablesContainer.innerHTML = '<p style="text-align: center;">No sales data found for your search.</p>';
                return;
            }

            for (const [monthYear, sales] of Object.entries(groupedData)) {
                const monthSection = document.createElement('div');
                monthSection.className = 'month-section';

                const monthHeader = document.createElement('div');
                monthHeader.className = 'month-header';
                monthHeader.style.display = 'flex';
                monthHeader.style.justifyContent = 'space-between';
                monthHeader.style.alignItems = 'center';

                const monthTitle = document.createElement('span');
                monthTitle.textContent = monthYear;
                monthHeader.appendChild(monthTitle);

                const backupButton = document.createElement('button');
                backupButton.className = 'backup-button';
                backupButton.textContent = 'Backup to Cloud';
                backupButton.onclick = () => backupMonthToCloudinary(monthYear, sales);
                monthHeader.appendChild(backupButton);

                monthSection.appendChild(monthHeader);

                // Create mobile summary section (will be shown only on mobile)
                const mobileSummarySection = document.createElement('div');
                mobileSummarySection.className = 'month-summary-mobile';

                // We'll populate this after calculating the totals
                monthSection.appendChild(mobileSummarySection);

                // Create table container for horizontal scrolling
                const tableContainer = document.createElement('div');
                tableContainer.className = 'table-container';

                const table = document.createElement('table');
                const thead = document.createElement('thead');
                thead.innerHTML = `
                    <tr>
                        <th>Reference Number</th>
                        <th>Date</th>
                        <th>Client Name</th>
                        <th>Billing Address</th>
                        <th>Billing Email</th>
                        <th>Billing Phone</th>
                        <th>Salesperson</th>
                        <th>Company</th>
                        <th>Item Purchase</th>
                        <th>Room</th>
                        <th>Quantity</th>
                        <th>Unit Cost (R)</th>
                        <th>Unit Price (R)</th>
                        <th>Profit/Unit (R)</th>
                        <th>Tax per Unit (R)</th>
                        <th>Payment Method</th>
                        <th>Total (R)</th>
                        <th>Total Profit (R)</th>
                    </tr>
                `;
                table.appendChild(thead);

                const tbody = document.createElement('tbody');
                let monthTotal = 0;
                let monthTotalTax = 0;
                let monthTotalProfit = 0;

                sales.forEach(sale => {
                    sale.items.forEach(item => {
                        const row = document.createElement('tr');

                        // Get basic values from the item
                        const quantity = parseFloat(item.quantity) || 0;
                        const taxPerProduct = parseFloat(item.tax_per_product) || 0;
                        const totalPrice = parseFloat(item.total_price) || 0;
                        const unitCost = parseFloat(item.unit_cost) || 0;

                        // Use the actual unit price from the sale (negotiated/discounted price)
                        const unitPrice = parseFloat(item.unit_price_excluding_tax) || (quantity > 0 ? totalPrice / quantity : 0);

                        // Calculate profit entirely on the frontend
                        const profitPerUnit = unitPrice - unitCost;
                        const totalProfit = profitPerUnit * quantity;

                        // Fetch room information for this item
                        const roomInfo = item.item_code ? `${item.room_name || 'N/A'}` : 'N/A';

                        row.innerHTML = `
                            <td>${sanitizeHTML(sale.reference_number)}</td>
                            <td>${sanitizeHTML(formatDate(sale.date))}</td>
                            <td>${sanitizeHTML(sale.billing_name)}</td>
                            <td>${sanitizeHTML(sale.billing_address)}</td>
                            <td>${sanitizeHTML(sale.billing_email)}</td>
                            <td>${sanitizeHTML(sale.billing_phone || 'N/A')}</td>
                            <td>${sanitizeHTML(sale.salesperson_name || 'N/A')}</td>
                            <td>${sanitizeHTML(sale.company_name || 'Shans Accessories PTY LTD')}</td>
                            <td>${sanitizeHTML(item.item_name)}</td>
                            <td>${sanitizeHTML(item.room_name || 'N/A')}</td>
                            <td>${quantity.toLocaleString()}</td>
                            <td>R ${unitCost.toFixed(2)}</td>
                            <td>R ${unitPrice.toFixed(2)}</td>
                            <td style="color: #28a745; font-weight: bold;">R ${profitPerUnit.toFixed(2)}</td>
                            <td>R ${taxPerProduct.toFixed(2)}</td>
                            <td>${sanitizeHTML(sale.payment_method)}</td>
                            <td>R ${totalPrice.toFixed(2)}</td>
                            <td style="color: #28a745; font-weight: bold;">R ${totalProfit.toFixed(2)}</td>
                        `;
                        tbody.appendChild(row);

                        monthTotal += totalPrice;
                        monthTotalTax += taxPerProduct * quantity;
                        monthTotalProfit += totalProfit; // Use the frontend-calculated profit
                    });
                });

                table.appendChild(tbody);

                // Add table to table container
                tableContainer.appendChild(table);
                monthSection.appendChild(tableContainer);

                // Get monthly expenses (month-specific: recurring + non-recurring for this month only)
                const monthExpenses = monthlyExpenses[monthYear] || 0;
                const netProfit = monthTotalProfit - monthExpenses;



                // Create the summary content
                const summaryContent = `
                    <div class="summary-item">
                        <span class="summary-label">Total Tax for ${monthYear}:</span>
                        <span class="summary-value">R ${monthTotalTax.toFixed(2)}</span>
                    </div>
                    <div class="summary-item profit-total">
                        <span class="summary-label">Gross Profit for ${monthYear}:</span>
                        <span class="summary-value">R ${monthTotalProfit.toFixed(2)}</span>
                    </div>
                    <div class="summary-item" style="background-color: #fff3cd; border-left: 4px solid #ffc107;">
                        <span class="summary-label">Total Expenses for ${monthYear}:</span>
                        <span class="summary-value">R ${monthExpenses.toFixed(2)}</span>
                    </div>
                    <div class="summary-item" style="background-color: ${netProfit >= 0 ? '#d4edda' : '#f8d7da'}; border-left: 4px solid ${netProfit >= 0 ? '#28a745' : '#dc3545'};">
                        <span class="summary-label">Net Profit for ${monthYear}:</span>
                        <span class="summary-value" style="color: ${netProfit >= 0 ? '#28a745' : '#dc3545'};">R ${netProfit.toFixed(2)}</span>
                    </div>
                    <div class="summary-item grand-total">
                        <span class="summary-label">Grand Total Sales for ${monthYear}:</span>
                        <span class="summary-value">R ${monthTotal.toFixed(2)}</span>
                    </div>
                `;

                // Populate the mobile summary (shown at top on mobile)
                mobileSummarySection.innerHTML = summaryContent;

                // Create a summary section at the bottom (shown on desktop)
                const summarySection = document.createElement('div');
                summarySection.className = 'month-summary';
                summarySection.innerHTML = summaryContent;
                monthSection.appendChild(summarySection);
                tablesContainer.appendChild(monthSection);
            }
        }

        function renderCards(data) {
            cardView.innerHTML = '';
            const groupedByMonth = {};

            data.forEach(sale => {
                const date = new Date(sale.date);
                const monthYear = date.toLocaleString('default', { month: 'long', year: 'numeric' });
                if (!groupedByMonth[monthYear]) {
                    groupedByMonth[monthYear] = [];
                }
                groupedByMonth[monthYear].push(sale);
            });

            for (const [monthYear, sales] of Object.entries(groupedByMonth)) {
                const monthHeader = document.createElement('div');
                monthHeader.className = 'month-header';
                monthHeader.textContent = monthYear;
                cardView.appendChild(monthHeader);

                // Calculate totals first for mobile summary
                const monthlyTotal = sales.reduce((total, sale) => {
                    return total + sale.items.reduce((itemTotal, item) => {
                        return itemTotal + (parseFloat(item.total_price) || 0);
                    }, 0);
                }, 0);

                const monthlyTaxTotal = sales.reduce((total, sale) => {
                    return total + sale.items.reduce((itemTotal, item) => {
                        const quantity = parseFloat(item.quantity) || 0;
                        const taxPerProduct = parseFloat(item.tax_per_product) || 0;
                        return itemTotal + (quantity * taxPerProduct);
                    }, 0);
                }, 0);

                const monthlyProfitTotal = sales.reduce((total, sale) => {
                    return total + sale.items.reduce((itemTotal, item) => {
                        // Calculate profit on the frontend for each item
                        const quantity = parseFloat(item.quantity) || 0;
                        const unitPrice = parseFloat(item.unit_price_excluding_tax) || 0;
                        const unitCost = parseFloat(item.unit_cost) || 0;
                        const profitPerUnit = unitPrice - unitCost;
                        const itemProfit = profitPerUnit * quantity;
                        return itemTotal + itemProfit;
                    }, 0);
                }, 0);

                // Get monthly expenses for card view (month-specific: recurring + non-recurring for this month only)
                const monthExpenses = monthlyExpenses[monthYear] || 0;
                const netProfit = monthlyProfitTotal - monthExpenses;



                // Add mobile summary card at the top (will be shown first on mobile)
                const mobileSummaryCard = document.createElement('div');
                mobileSummaryCard.className = 'card mobile-summary-card';
                mobileSummaryCard.innerHTML = `
                    <h3>📊 Monthly Totals for ${monthYear}</h3>
                    <p><strong>Total Tax:</strong> <span>R ${monthlyTaxTotal.toFixed(2)}</span></p>
                    <p style="padding: 8px; background-color: #e6ffe6; border-left: 4px solid #28a745; border-radius: 4px; margin: 10px 0;">
                        <strong>Gross Profit:</strong>
                        <span style="font-weight: bold; color: #28a745; font-size: 16px;">R ${monthlyProfitTotal.toFixed(2)}</span>
                    </p>
                    <p style="padding: 8px; background-color: #fff3cd; border-left: 4px solid #ffc107; border-radius: 4px; margin: 10px 0;">
                        <strong>Total Expenses for ${monthYear}:</strong>
                        <span style="font-weight: bold; color: #856404; font-size: 16px;">R ${monthExpenses.toFixed(2)}</span>
                    </p>
                    <p style="padding: 8px; background-color: ${netProfit >= 0 ? '#d4edda' : '#f8d7da'}; border-left: 4px solid ${netProfit >= 0 ? '#28a745' : '#dc3545'}; border-radius: 4px; margin: 10px 0;">
                        <strong>Net Profit:</strong>
                        <span style="font-weight: bold; color: ${netProfit >= 0 ? '#28a745' : '#dc3545'}; font-size: 18px;">R ${netProfit.toFixed(2)}</span>
                    </p>
                    <p><strong>Total Sales:</strong> <span style="font-weight: bold; color: #0066cc; font-size: 16px;">R ${monthlyTotal.toFixed(2)}</span></p>
                `;
                mobileSummaryCard.style.backgroundColor = '#f0f7ff';
                mobileSummaryCard.style.border = '2px solid #007BFF';
                cardView.appendChild(mobileSummaryCard);

                sales.forEach(sale => {
                    sale.items.forEach(item => {
                        const quantity = parseFloat(item.quantity) || 0;
                        const taxPerProduct = parseFloat(item.tax_per_product) || 0;
                        const totalPrice = parseFloat(item.total_price) || 0;

                        // Use the actual unit price from the sale (negotiated/discounted price)
                        const unitPrice = parseFloat(item.unit_price_excluding_tax) || (quantity > 0 ? totalPrice / quantity : 0);

                        const card = document.createElement('div');
                        card.className = 'card';

                        // Get the unit cost from the product data
                        const unitCost = parseFloat(item.unit_cost) || 0;

                        // Calculate profit entirely on the frontend
                        const profitPerUnit = unitPrice - unitCost;
                        const totalProfit = profitPerUnit * quantity;

                        card.innerHTML = `
                            <h3>${sanitizeHTML(formatDate(sale.date))} - ${sanitizeHTML(sale.reference_number)}</h3>
                            <p><strong>Client Name:</strong> <span>${sanitizeHTML(sale.billing_name)}</span></p>
                            <p><strong>Billing Address:</strong> <span>${sanitizeHTML(sale.billing_address)}</span></p>
                            <p><strong>Billing Email:</strong> <span>${sanitizeHTML(sale.billing_email)}</span></p>
                            <p><strong>Billing Phone:</strong> <span>${sanitizeHTML(sale.billing_phone || 'N/A')}</span></p>
                            <p><strong>Salesperson:</strong> <span>${sanitizeHTML(sale.salesperson_name || 'N/A')}</span></p>
                            <p><strong>Company:</strong> <span>${sanitizeHTML(sale.company_name || 'Shans Accessories PTY LTD')}</span></p>
                            <p><strong>Item:</strong> <span>${sanitizeHTML(item.item_name)}</span></p>
                            <p><strong>Room:</strong> <span>${sanitizeHTML(item.room_name || 'N/A')}</span></p>
                            <p><strong>Quantity:</strong> <span>${quantity.toLocaleString()}</span></p>
                            <p><strong>Unit Price:</strong> <span>R ${unitPrice.toFixed(2)}</span></p>
                            <p><strong>Unit Cost:</strong> <span>R ${unitCost.toFixed(2)}</span></p>
                            <p><strong>Profit per Unit:</strong> <span style="color: #28a745;">R ${profitPerUnit.toFixed(2)}</span></p>
                            <p><strong>Tax per Unit:</strong> <span>R ${taxPerProduct.toFixed(2)}</span></p>
                            <p><strong>Total Value:</strong> <span>R ${(unitPrice * quantity).toFixed(2)}</span></p>
                            <p><strong>Total Cost:</strong> <span>R ${(unitCost * quantity).toFixed(2)}</span></p>
                            <p><strong>Total Profit:</strong> <span style="color: #28a745;">R ${totalProfit.toFixed(2)}</span></p>
                            <p><strong>Total Tax:</strong> <span>R ${(taxPerProduct * quantity).toFixed(2)}</span></p>
                            <p><strong>Payment Method:</strong> <span>${sanitizeHTML(sale.payment_method)}</span></p>
                            <p><strong>Total Price:</strong> <span style="font-weight: bold; color: #0066cc;">R ${totalPrice.toFixed(2)}</span></p>
                        `;
                        cardView.appendChild(card);
                    });
                });


            }
        }

        function saveToExcel() {
            if (filteredSalesData.length === 0) {
                showStatusMessage('No data available to save!', 'error');
                return;
            }

            const flattenedData = filteredSalesData.flatMap(sale =>
                sale.items.map(item => {
                    const quantity = parseFloat(item.quantity) || 0;
                    const totalPrice = parseFloat(item.total_price) || 0;

                    // Use the actual unit price from the sale (negotiated/discounted price)
                    const unitPrice = parseFloat(item.unit_price_excluding_tax) || (quantity > 0 ? totalPrice / quantity : 0);

                    // Get the unit cost from the product data
                    const unitCost = parseFloat(item.unit_cost) || 0;

                    // Calculate profit entirely on the frontend
                    const profitPerUnit = unitPrice - unitCost;
                    const totalProfit = profitPerUnit * quantity;

                    // Calculate total values
                    const totalRetailValue = unitPrice * quantity;
                    const totalCost = unitCost * quantity;

                    return {
                        "Reference Number": sale.reference_number,
                        "Date": formatDate(sale.date),
                        "Client Name": sale.billing_name,
                        "Billing Address": sale.billing_address,
                        "Billing Email": sale.billing_email,
                        "Billing Phone": sale.billing_phone || 'N/A',
                        "Salesperson": sale.salesperson_name || 'N/A',
                        "Company": sale.company_name || 'Shans Accessories PTY LTD',
                        "Item Purchased": item.item_name,
                        "Room": item.room_name || 'N/A',
                        "Quantity": quantity,
                        "Unit Price (R)": unitPrice.toFixed(2),
                        "Unit Cost (R)": unitCost.toFixed(2),
                        "Profit per Unit (R)": profitPerUnit.toFixed(2),
                        "Tax per Unit (R)": (parseFloat(item.tax_per_product) || 0).toFixed(2),
                        "Total Value (R)": totalRetailValue.toFixed(2),
                        "Total Cost (R)": totalCost.toFixed(2),
                        "Total Profit (R)": totalProfit.toFixed(2),
                        "Total Tax (R)": (parseFloat(item.tax_per_product || 0) * quantity).toFixed(2),
                        "Payment Method": sale.payment_method,
                        "Total Price (R)": totalPrice.toFixed(2)
                    };
                })
            );

            const worksheet = XLSX.utils.json_to_sheet(flattenedData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sales Data');
            XLSX.writeFile(workbook, `Sales_Data_${new Date().toISOString().split('T')[0]}.xlsx`);
        }

        function toggleView() {
            const tables = document.getElementById('tables-container');
            if (tables.style.display === 'none') {
                tables.style.display = 'block';
                cardView.style.display = 'none';
            } else {
                tables.style.display = 'none';
                cardView.style.display = 'grid';
            }
        }

        function printSalesData() {
            // Make sure we're in table view before printing
            if (tablesContainer.style.display === 'none') {
                tablesContainer.style.display = 'block';
                cardView.style.display = 'none';
            }

            // Print the page
            window.print();
        }

        function applyAllFilters() {
            const searchQuery = searchBar.value.trim().toLowerCase();
            const selectedCompany = companyFilter.value;
            const selectedPayment = paymentFilter.value;
            const selectedSalesperson = salespersonFilter.value;
            const selectedMonth = monthFilter.value;

            filteredSalesData = allSalesData.filter(sale => {
                // Text search filter
                const matchesSearch = !searchQuery || (
                    sale.reference_number.toLowerCase().includes(searchQuery) ||
                    sale.date.toLowerCase().includes(searchQuery) ||
                    sale.billing_name.toLowerCase().includes(searchQuery) ||
                    sale.billing_address.toLowerCase().includes(searchQuery) ||
                    sale.billing_email.toLowerCase().includes(searchQuery) ||
                    (sale.billing_phone && sale.billing_phone.toLowerCase().includes(searchQuery)) ||
                    (sale.salesperson_name && sale.salesperson_name.toLowerCase().includes(searchQuery)) ||
                    (sale.company_name && sale.company_name.toLowerCase().includes(searchQuery)) ||
                    sale.payment_method.toLowerCase().includes(searchQuery) ||
                    sale.items.some(item =>
                        item.item_name.toLowerCase().includes(searchQuery) ||
                        (item.room_name && item.room_name.toLowerCase().includes(searchQuery)) ||
                        String(item.quantity).includes(searchQuery) ||
                        String(item.tax_per_product).includes(searchQuery) ||
                        String(item.total_price).includes(searchQuery)
                    )
                );

                // Company filter
                const matchesCompany = !selectedCompany || (sale.company_name || 'Shans Accessories PTY LTD') === selectedCompany;

                // Payment method filter
                const matchesPayment = !selectedPayment || sale.payment_method === selectedPayment;

                // Salesperson filter
                const matchesSalesperson = !selectedSalesperson || sale.salesperson_name === selectedSalesperson;

                // Month filter
                const matchesMonth = !selectedMonth || (() => {
                    const saleDate = new Date(sale.date);
                    const saleMonthYear = saleDate.toLocaleString('default', { month: 'long', year: 'numeric' });
                    return saleMonthYear === selectedMonth;
                })();

                return matchesSearch && matchesCompany && matchesPayment && matchesSalesperson && matchesMonth;
            });

            groupDataByMonth(filteredSalesData);
            renderTables(groupedSalesData);
            renderCards(filteredSalesData);
        }

        function filterAndRender(query) {
            applyAllFilters();
        }

        function sanitizeHTML(str) {
            if (str === null || str === undefined) return '';
            const temp = document.createElement('div');
            temp.textContent = str;
            return temp.innerHTML;
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const options = { day: 'numeric', month: 'long', year: 'numeric' };
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', options);
        }

        function debounce(func, delay) {
            let debounceTimer;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => func.apply(context, args), delay);
            }
        }

        viewToggle.addEventListener('click', toggleView);
        searchBar.addEventListener('input', debounce((e) => {
            const query = e.target.value.trim();
            filterAndRender(query);
        }, 300));

        // Filter event listeners
        companyFilter.addEventListener('change', applyAllFilters);
        paymentFilter.addEventListener('change', applyAllFilters);
        salespersonFilter.addEventListener('change', applyAllFilters);
        monthFilter.addEventListener('change', applyAllFilters);

        // Clear filters functionality
        clearFiltersBtn.addEventListener('click', () => {
            searchBar.value = '';
            companyFilter.value = '';
            paymentFilter.value = '';
            salespersonFilter.value = '';
            monthFilter.value = '';
            applyAllFilters();
        });

        // Backend API configuration
        const BACKUP_CONFIG = {
            backupEndpoint: `${getApiBaseUrl()}/backup/backup-sales-data`,
            excelUploadEndpoint: `${getApiBaseUrl()}/backup/upload-excel-backup`, // NEW: Excel upload endpoint
            listBackupsEndpoint: `${getApiBaseUrl()}/backup/list-backups`,
            testConnectionEndpoint: `${getApiBaseUrl()}/backup/test-connection`
        };

        // Helper function to convert blob to base64
        function blobToBase64(blob) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(blob);
            });
        }

        // Helper function to format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // Toast notification function
        function showToast(message, type = 'info', duration = 5000) {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;

            toastContainer.appendChild(toast);

            // Trigger animation
            setTimeout(() => toast.classList.add('show'), 100);

            // Remove toast after duration
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, duration);
        }

        // NEW: Enhanced backup function that creates Excel on frontend first
        async function backupMonthToCloudinary(monthYear, salesData) {
            const button = event.target;
            const originalText = button.textContent;

            try {
                // Disable button and show loading state
                button.disabled = true;
                button.textContent = 'Preparing...';
                showToast(`🔄 Preparing Excel backup for ${monthYear}...`, 'info', 3000);

                // Get authentication token
                const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');

                if (!token) {
                    throw new Error('Authentication required. Please log in again.');
                }

                // Step 1: Create Excel file on frontend
                button.textContent = 'Creating Excel...';
                showToast(`📊 Creating Excel file for ${monthYear}...`, 'info', 3000);

                const flattenedData = salesData.flatMap(sale =>
                    sale.items.map(item => {
                        // Use the EXACT same calculation logic as the table display
                        const quantity = parseFloat(item.quantity) || 0;
                        const taxPerProduct = parseFloat(item.tax_per_product) || 0;
                        const totalPrice = parseFloat(item.total_price) || 0;
                        const unitCost = parseFloat(item.unit_cost) || 0;

                        // Use the actual unit price from the sale (negotiated/discounted price)
                        const unitPrice = parseFloat(item.unit_price_excluding_tax) || (quantity > 0 ? totalPrice / quantity : 0);

                        // Calculate profit entirely on the frontend (same as table)
                        const profitPerUnit = unitPrice - unitCost;
                        const totalProfit = profitPerUnit * quantity;

                        return {
                            "Reference Number": sale.reference_number,
                            "Date": formatDate(sale.date),
                            "Client Name": sale.billing_name,
                            "Billing Address": sale.billing_address,
                            "Billing Email": sale.billing_email,
                            "Billing Phone": sale.billing_phone || 'N/A',
                            "Salesperson": sale.salesperson_name || 'N/A',
                            "Company": sale.company_name || 'Shans Accessories PTY LTD',
                            "Item Purchase": item.item_name,
                            "Room": item.room_name || 'N/A',
                            "Quantity": quantity,
                            "Unit Cost (R)": unitCost.toFixed(2),
                            "Unit Price (R)": unitPrice.toFixed(2),
                            "Profit/Unit (R)": profitPerUnit.toFixed(2),
                            "Tax per Unit (R)": taxPerProduct.toFixed(2),
                            "Payment Method": sale.payment_method,
                            "Total (R)": totalPrice.toFixed(2),
                            "Total Profit (R)": totalProfit.toFixed(2)
                        };
                    })
                );

                // Create Excel workbook
                const worksheet = XLSX.utils.json_to_sheet(flattenedData);
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, `Sales ${monthYear}`);

                // Generate Excel file as blob
                const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
                const excelBlob = new Blob([excelBuffer], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });

                // Step 2: Upload Excel file to backend
                button.textContent = 'Uploading...';
                showToast(`📤 Uploading Excel backup for ${monthYear} to cloud storage...`, 'info', 3000);

                // Create FormData with the Excel file
                const formData = new FormData();
                const timestamp = new Date().toISOString().split('T')[0];
                const filename = `Sales_Backup_${monthYear.replace(/\s+/g, '_')}_${timestamp}.xlsx`;
                formData.append('excelFile', excelBlob, filename);
                formData.append('monthYear', monthYear);

                // Upload to backend
                const response = await fetch(BACKUP_CONFIG.excelUploadEndpoint, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                        // Don't set Content-Type - let browser set it with boundary for FormData
                    },
                    body: formData
                });

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const textResponse = await response.text();
                    throw new Error(`Server returned non-JSON response (${response.status}): ${textResponse.substring(0, 200)}...`);
                }

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || `Backup failed: ${response.statusText}`);
                }

                // Show success message
                showToast(
                    `✅ Excel backup successful! File "${result.filename}" has been saved to cloud storage. ${flattenedData.length} records backed up.`,
                    'success',
                    7000
                );

            } catch (error) {

                // If the new endpoint fails, try the original backup method as fallback
                if (error.message.includes('404') || error.message.includes('Not Found')) {
                    try {
                        button.textContent = 'Trying fallback...';
                        showToast(`🔄 Trying alternative backup method...`, 'info', 3000);

                        // Use original backup endpoint as fallback
                        const fallbackResponse = await fetch(BACKUP_CONFIG.backupEndpoint, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify({
                                monthYear: monthYear,
                                salesData: salesData
                            })
                        });

                        const fallbackResult = await fallbackResponse.json();

                        if (!fallbackResponse.ok) {
                            throw new Error(fallbackResult.error || `Fallback backup failed: ${fallbackResponse.statusText}`);
                        }

                        showToast(
                            `✅ Backup successful (fallback method)! File "${fallbackResult.filename}" has been saved to cloud storage. ${fallbackResult.recordCount} records backed up.`,
                            'success',
                            7000
                        );
                        console.log('Fallback backup details:', fallbackResult);
                        return; // Exit successfully

                    } catch (fallbackError) {
                        console.error('Fallback backup also failed:', fallbackError);
                        showToast(`❌ Both backup methods failed. Please contact support.`, 'error', 8000);
                        return;
                    }
                }

                // Handle specific error types
                if (error.message.includes('Authentication')) {
                    showToast(`🔒 ${error.message}`, 'error', 8000);
                } else if (error.message.includes('Only Excel files')) {
                    showToast(`❌ File format error: ${error.message}`, 'error', 8000);
                } else {
                    showToast(`❌ Excel backup failed: ${error.message}. Please try again or contact support.`, 'error', 8000);
                }
            } finally {
                // Re-enable button
                button.disabled = false;
                button.textContent = originalText;
            }
        }



        // Add event listeners
        document.addEventListener('DOMContentLoaded', fetchSalesData);
    </script>
</body>
</html>